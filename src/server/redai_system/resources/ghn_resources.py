"""
GHN Resources cho FastMCP 2.0 Shipment Server

<PERSON><PERSON><PERSON><PERSON> gộp tất cả các resources liên quan đến GHN API
Bao gồm: thông tin đơn hàng, đ<PERSON><PERSON> đ<PERSON>, d<PERSON><PERSON> v<PERSON>, hỗ tr<PERSON>ử dụng FastMCP 2.0 framework với HTTP transport
"""

import json
from fastmcp import FastMCP

# Import với fallback
try:
    from ..utils import make_ghn_request, GHN_SHOP_ID
except ImportError:
    from utils import make_ghn_request, GHN_SHOP_ID

def register_ghn_resources(mcp: FastMCP):
    """Đăng ký tất cả GHN resources với MCP server"""
    
    # ORDER RESOURCES - Thông tin đơn hàng
    @mcp.resource("ghn://order/{order_code}")
    async def get_order_info(order_code: str) -> str:
        """
        Lấy thông tin chi tiết đơn hàng theo mã đơn GHN

        Args:
            order_code: Mã đơn hàng GHN

        Returns:
            JSON string chứa thông tin chi tiết đơn hàng
        """
        data = {"order_code": order_code}

        try:
            result = await make_ghn_request(
                "shiip/public-api/v2/shipping-order/detail",
                method="POST",
                data=data
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy thông tin đơn hàng: {str(e)}"

    @mcp.resource("ghn://order-by-client/{client_order_code}")
    async def get_order_info_by_client_code(client_order_code: str) -> str:
        """
        Lấy thông tin đơn hàng theo mã đơn của khách hàng

        Args:
            client_order_code: Mã đơn hàng của khách hàng

        Returns:
            JSON string chứa thông tin đơn hàng
        """
        data = {"client_order_code": client_order_code}

        try:
            result = await make_ghn_request(
                "shiip/public-api/v2/shipping-order/detail-by-client-code",
                method="POST",
                data=data
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy thông tin đơn hàng theo mã khách: {str(e)}"

    # LOCATION RESOURCES - Thông tin địa điểm
    @mcp.resource("ghn://provinces")
    async def get_provinces() -> str:
        """
        Lấy danh sách các tỉnh/thành phố hỗ trợ

        Returns:
            JSON string chứa danh sách tỉnh/thành phố
        """
        try:
            result = await make_ghn_request(
                "public-api/master-data/province",
                method="GET"
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách tỉnh/thành phố: {str(e)}"

    @mcp.resource("ghn://districts/{province_id}")
    async def get_districts(province_id: str) -> str:
        """
        Lấy danh sách quận/huyện theo tỉnh/thành phố

        Args:
            province_id: ID tỉnh/thành phố

        Returns:
            JSON string chứa danh sách quận/huyện
        """
        try:
            result = await make_ghn_request(
                f"public-api/master-data/district?province_id={province_id}",
                method="GET"
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách quận/huyện: {str(e)}"

    @mcp.resource("ghn://wards/{district_id}")
    async def get_wards(district_id: str) -> str:
        """
        Lấy danh sách phường/xã theo quận/huyện

        Args:
            district_id: ID quận/huyện

        Returns:
            JSON string chứa danh sách phường/xã
        """
        try:
            result = await make_ghn_request(
                f"public-api/master-data/ward?district_id={district_id}",
                method="GET"
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách phường/xã: {str(e)}"

    # SERVICE RESOURCES - Dịch vụ và cửa hàng
    @mcp.resource("ghn://services/{from_district}/{to_district}")
    async def get_services(from_district: str, to_district: str) -> str:
        """
        Lấy danh sách dịch vụ vận chuyển phù hợp với tuyến đường

        Args:
            from_district: ID quận/huyện lấy hàng
            to_district: ID quận/huyện giao hàng

        Returns:
            JSON string chứa danh sách dịch vụ
        """
        data = {
            "shop_id": int(GHN_SHOP_ID),
            "from_district": int(from_district),
            "to_district": int(to_district)
        }

        try:
            result = await make_ghn_request(
                "shiip/public-api/v2/shipping-order/available-services",
                method="POST",
                data=data
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách dịch vụ: {str(e)}"

    @mcp.resource("ghn://stations/{district_id}")
    async def get_stations(district_id: str) -> str:
        """
        Lấy danh sách bưu cục GHN theo quận/huyện

        Args:
            district_id: ID quận/huyện

        Returns:
            JSON string chứa danh sách bưu cục
        """
        data = {
            "district_id": int(district_id),
            "offset": 0,
            "limit": 50
        }

        try:
            result = await make_ghn_request(
                "shiip/public-api/v2/station/get",
                method="POST",
                data=data
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách bưu cục: {str(e)}"

    @mcp.resource("ghn://stores")
    async def get_stores() -> str:
        """
        Lấy danh sách các cửa hàng của khách hàng

        Returns:
            JSON string chứa danh sách cửa hàng
        """
        data = {
            "offset": 0,
            "limit": 50
        }

        try:
            result = await make_ghn_request(
                "shiip/public-api/v2/shop/all",
                method="POST",
                data=data
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách cửa hàng: {str(e)}"

    @mcp.resource("ghn://pick-shifts")
    async def get_pick_shifts() -> str:
        """
        Lấy danh sách ca lấy hàng sắp tới

        Returns:
            JSON string chứa danh sách ca lấy hàng
        """
        try:
            result = await make_ghn_request(
                "shiip/public-api/v2/shift/date",
                method="GET"
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách ca lấy hàng: {str(e)}"

    # SUPPORT RESOURCES - Hỗ trợ khách hàng
    @mcp.resource("ghn://tickets")
    async def get_tickets() -> str:
        """
        Lấy danh sách các ticket hỗ trợ của cửa hàng

        Returns:
            JSON string chứa danh sách ticket
        """
        try:
            result = await make_ghn_request(
                "public-api/ticket/index",
                method="GET"
            )
            return json.dumps(result, ensure_ascii=False, indent=2)
        except Exception as e:
            return f"Lỗi khi lấy danh sách ticket: {str(e)}"
