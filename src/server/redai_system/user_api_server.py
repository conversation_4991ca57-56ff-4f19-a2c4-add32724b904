"""
Server MCP cho RedAI User API sử dụng FastMCP và OpenAPI Schema

Server này tự động tạo các MCP tools và resources từ swagger-user-schema.json,
cung cấp giao diện MCP cho tất cả các endpoint của User API.

Tính năng:
- Tự động tạo tools từ OpenAPI specification
- Hỗ trợ JWT authentication
- HTTP transport với FastMCP
- Tùy chỉnh route mapping cho các loại endpoint khác nhau
- Xử lý parameters, headers và request body tự động

Cấu trúc API:
- Media endpoints: Quản lý media files của user
- Account endpoints: Quản lý thông tin tài khoản, ngân hàng, doanh nghiệp
- Knowledge files: Quản lý files tri thức
- Notification settings: Cài đặt thông báo

Authentication:
- JWT token qua header Authorization: Bearer <token>
- Tự động xử lý authentication cho tất cả requests
"""

import os
import json
import httpx
from pathlib import Path
from typing import Optional

# FastMCP imports
from fastmcp import FastMCP
from fastmcp.server.openapi import RouteMap, MCPType, HTTPRoute, OpenAPITool, OpenAPIResource, OpenAPIResourceTemplate

# Cấu hình môi trường
API_BASE_URL = os.getenv("REDAI_USER_API_BASE_URL", "https://api.redai.com")
JWT_TOKEN = os.getenv("REDAI_JWT_TOKEN", "")
HTTP_HOST = os.getenv("USER_API_HTTP_HOST", "127.0.0.1")
HTTP_PORT = int(os.getenv("USER_API_HTTP_PORT", "8001"))
HTTP_PATH = os.getenv("USER_API_HTTP_PATH", "/mcp")

def load_openapi_schema() -> dict:
    """
    Tải OpenAPI schema từ file swagger-user-schema.json
    """
    schema_path = Path(__file__).parent / "swagger-user-schema.json"
    
    if not schema_path.exists():
        raise FileNotFoundError(f"Không tìm thấy file schema tại: {schema_path}")
    
    with open(schema_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def create_authenticated_client(base_url: str, jwt_token: Optional[str] = None) -> httpx.AsyncClient:
    """
    Tạo HTTP client với authentication
    """
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    if jwt_token:
        headers["Authorization"] = f"Bearer {jwt_token}"
    
    return httpx.AsyncClient(
        base_url=base_url,
        headers=headers,
        timeout=30.0
    )

def create_route_maps() -> list[RouteMap]:
    """
    Tạo custom route mappings cho các loại endpoint khác nhau
    """
    return [
        # GET endpoints với path parameters thành ResourceTemplates
        RouteMap(
            methods=["GET"],
            pattern=r".*\{.*\}.*",  # Có path parameters như {id}
            mcp_type=MCPType.RESOURCE_TEMPLATE,
            mcp_tags={"detail-view", "parameterized", "user-api"}
        ),
        
        # GET endpoints khác thành Resources
        RouteMap(
            methods=["GET"],
            pattern=r".*",
            mcp_type=MCPType.RESOURCE,
            mcp_tags={"list-data", "user-api"}
        ),
        
        # POST, PUT, DELETE endpoints thành Tools
        RouteMap(
            methods=["POST", "PUT", "DELETE"],
            pattern=r".*",
            mcp_type=MCPType.TOOL,
            mcp_tags={"action", "user-api"}
        ),
    ]

def customize_component_names() -> dict[str, str]:
    """
    Tùy chỉnh tên cho các MCP components
    """
    return {
        # Media endpoints
        "MediaUserController_findMyMedia_v1": "get_my_media",
        "MediaUserController_deleteManyMyMedia_v1": "delete_my_media",
        "MediaUserController_findOne_v1": "get_media_by_id",
        "MediaUserController_createPresignedUrlsFromMediaList_v1": "create_media_presigned_urls",
        
        # Account endpoints
        "AccountController_updateBankInfo_v1": "update_bank_info",
        "AccountController_getBankInfo_v1": "get_bank_info",
        "AccountController_getBusinessInfo_v1": "get_business_info",
        "AccountController_updateBusinessInfo_v1": "update_business_info",
        "AccountController_createAvatarUploadUrl_v1": "create_avatar_upload_url",
        "AccountController_updateAvatar_v1": "update_avatar",
        "AccountController_getNotificationSettings_v1": "get_notification_settings",
        "AccountController_updateNotificationSettings_v1": "update_notification_settings",
        
        # Knowledge files endpoints
        "KnowledgeFileUserController_batchCreateFiles_v1": "batch_create_knowledge_files",
    }

def customize_mcp_components(
    route: HTTPRoute, 
    component: OpenAPITool | OpenAPIResource | OpenAPIResourceTemplate,
) -> None:
    """
    Tùy chỉnh MCP components sau khi được tạo
    """
    # Thêm prefix cho description dựa trên loại component
    if isinstance(component, OpenAPITool):
        component.description = f"🔧 {component.description}"
        component.tags.add("api-action")
    elif isinstance(component, OpenAPIResource):
        component.description = f"📊 {component.description}"
        component.tags.add("api-data")
    elif isinstance(component, OpenAPIResourceTemplate):
        component.description = f"📋 {component.description}"
        component.tags.add("api-detail")
    
    # Thêm tags dựa trên path
    if "/media/" in route.path:
        component.tags.add("media")
    elif "/account/" in route.path:
        component.tags.add("account")
    elif "/knowledge-files/" in route.path:
        component.tags.add("knowledge")
    
    # Thêm security info cho authenticated endpoints
    if route.openapi_route.get("security"):
        component.tags.add("authenticated")
        component.description += " (Yêu cầu JWT authentication)"

def create_user_api_server() -> FastMCP:
    """
    Tạo MCP server từ OpenAPI schema
    """
    # Tải OpenAPI schema
    openapi_spec = load_openapi_schema()
    
    # Tạo authenticated HTTP client
    api_client = create_authenticated_client(API_BASE_URL, JWT_TOKEN)
    
    # Tạo MCP server từ OpenAPI spec
    mcp = FastMCP.from_openapi(
        openapi_spec=openapi_spec,
        client=api_client,
        name="RedAI-User-API-Server",
        timeout=30.0,
        tags={"redai", "user-api", "openapi"},
        route_maps=create_route_maps(),
        mcp_names=customize_component_names(),
        mcp_component_fn=customize_mcp_components,
    )
    
    return mcp

# Tạo MCP server instance
mcp = create_user_api_server()

def main():
    """
    Hàm main để khởi chạy MCP server
    """
    try:
        # Thiết lập encoding cho Windows console
        import sys
        if sys.platform == "win32":
            import os
            os.system("chcp 65001 > nul")  # Set UTF-8 encoding
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
        
        print("="*70)
        print("🚀 Khởi động RedAI User API MCP Server")
        print("="*70)
        print("📋 Cấu hình:")
        print(f"   🌐 API Base URL: {API_BASE_URL}")
        print(f"   🔑 JWT Token: {'✅ Đã cấu hình' if JWT_TOKEN else '❌ Chưa cấu hình'}")
        print(f"   🚀 Transport: Streamable HTTP")
        print(f"   🌐 Server URL: http://{HTTP_HOST}:{HTTP_PORT}")
        print(f"   📡 MCP Endpoint: http://{HTTP_HOST}:{HTTP_PORT}{HTTP_PATH}")
        print()
        
        # Tải schema để hiển thị thông tin
        schema = load_openapi_schema()
        endpoints = list(schema.get("paths", {}).keys())
        
        print(f"📊 API Information:")
        print(f"   📖 Title: {schema.get('info', {}).get('title', 'N/A')}")
        print(f"   📝 Description: {schema.get('info', {}).get('description', 'N/A')}")
        print(f"   🔢 Version: {schema.get('info', {}).get('version', 'N/A')}")
        print(f"   📍 Endpoints: {len(endpoints)} endpoints")
        print()
        
        print("📋 Available Endpoints:")
        for path in endpoints:
            methods = list(schema["paths"][path].keys())
            print(f"   • {path} [{', '.join(method.upper() for method in methods)}]")
        
        if not JWT_TOKEN:
            print()
            print("⚠️  CẢNH BÁO: JWT Token chưa được cấu hình!")
            print("📋 Để sử dụng API thực tế:")
            print("   1. Thiết lập biến môi trường REDAI_JWT_TOKEN")
            print("   2. Hoặc cập nhật JWT_TOKEN trong code")
            print("   3. Khởi động lại server")
            print("🧪 Server vẫn có thể chạy để test cấu trúc MCP")
        
        print("="*70)
        print("🚀 Đang khởi động server...")
        
        # Chạy MCP server với Streamable HTTP transport
        mcp.run(
            transport="streamable-http",
            host=HTTP_HOST,
            port=HTTP_PORT,
            path=HTTP_PATH
        )
        
    except KeyboardInterrupt:
        print("\n⏹️  Server đã được dừng bởi người dùng")
    except Exception as e:
        print(f"❌ Lỗi khi khởi động server: {str(e)}")
        import traceback
        traceback.print_exc()
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
